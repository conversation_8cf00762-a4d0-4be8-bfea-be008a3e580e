#!/bin/bash

# Simple compilation script for MCRound Cab Booking Application
# Replaces the complex Makefile with a simple script

echo "Compiling MCRound Cab Booking Application..."

# Create output directory if it doesn't exist
mkdir -p bin

# Compile all source files into a single executable
g++ -std=c++17 -Wall -Wextra -O2 -I./src \
    src/main.cpp \
    src/App.cpp \
    src/utils/Parser.cpp \
    src/models/Location.cpp \
    src/models/User.cpp \
    src/models/Vehicle.cpp \
    src/models/Driver.cpp \
    src/models/Ride.cpp \
    src/repositories/UserRepository.cpp \
    src/repositories/DriverRepository.cpp \
    src/repositories/RideRepository.cpp \
    src/services/UserService.cpp \
    src/services/DriverService.cpp \
    src/services/BillingService.cpp \
    src/services/RideService.cpp \
    -o bin/cab_booking

if [ $? -eq 0 ]; then
    echo "✅ Compilation successful! Executable created at: bin/cab_booking"
    echo "Run with: ./bin/cab_booking"
else
    echo "❌ Compilation failed!"
    exit 1
fi
