#!/bin/bash

# Clean script for MCRound Cab Booking Application
# Removes compiled binaries and temporary files

echo "Cleaning MCRound project..."

# Remove binary directory
if [ -d "bin" ]; then
    rm -rf bin
    echo "✅ Removed bin/ directory"
else
    echo "ℹ️  bin/ directory not found"
fi

# Remove any temporary files
find . -name "*.o" -delete 2>/dev/null
find . -name "*.tmp" -delete 2>/dev/null
find . -name "*~" -delete 2>/dev/null

echo "✅ Cleanup complete!"
