# MCRound - Cab Booking Application

A simple command-line cab booking application written in C++.

## Features

- Add and manage users
- Add and manage drivers with vehicles
- Find available rides
- Book and complete rides
- Calculate bills and track earnings

## Building the Application

### Simple Method (Recommended)
```bash
./compile.sh
```

### Manual Compilation
```bash
g++ -std=c++17 -Wall -Wextra -O2 -I./src src/**/*.cpp -o bin/cab_booking
```

## Running the Application

```bash
./bin/cab_booking
```

## Available Commands

- `add_user <Name> <Gender> <Age>` - Add a new user
- `update_user <Name> <Gender> <Age>` - Update user information
- `update_userLocation <Name> (<x,y>)` - Update user location
- `add_driver <Name> <Gender> <Age> <VehicleModel> <RegNo> (<x,y>)` - Add a new driver
- `update_driverLocation <Name> (<x,y>)` - Update driver location
- `change_driver_status <Name> <available|unavailable>` - Change driver availability
- `find_ride <UserName> (<sx,sy>) (<dx,dy>)` - Find available rides
- `choose_ride <UserName> <DriverName> (<sx,sy>) (<dx,dy>)` - Book a ride
- `complete_ride <UserName>` - Complete an active ride
- `calculate_bill <UserName> (<sx,sy>) (<dx,dy>)` - Calculate ride bill
- `find_total_earning` - Show total earnings of all drivers
- `help` - Show help message
- `quit` or `exit` - Exit the application

## Code Structure

The application uses a clean, simplified C++ syntax with:
- `bits/stdc++.h` header for all standard library includes
- `using namespace std` for cleaner syntax
- Simple compilation without complex build systems
- Modular architecture with separate models, repositories, and services

## Example Usage

```
> add_user John Male 25
User added
> add_driver Alice Female 30 Toyota ABC123 (0,0)
Driver added
> find_ride John (1,1) (5,5)
Available drivers: Alice
> choose_ride John Alice (1,1) (5,5)
Ride chosen. Bill: 57
> complete_ride John
Ride completed
```
