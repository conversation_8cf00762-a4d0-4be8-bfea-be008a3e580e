# Simple Makefile for the Cab Booking CLI

CXX := g++
CXXFLAGS := -std=c++17 -Wall -Wextra -O2
INC := -I./src
SRC_DIR := src
BUILD_DIR := build
TARGET := cab_booking

SRCS := \
	$(SRC_DIR)/main.cpp \
	$(SRC_DIR)/App.cpp \
	$(SRC_DIR)/utils/Parser.cpp \
	$(SRC_DIR)/models/Location.cpp \
	$(SRC_DIR)/models/User.cpp \
	$(SRC_DIR)/models/Vehicle.cpp \
	$(SRC_DIR)/models/Driver.cpp \
	$(SRC_DIR)/models/Ride.cpp \
	$(SRC_DIR)/repositories/UserRepository.cpp \
	$(SRC_DIR)/repositories/DriverRepository.cpp \
	$(SRC_DIR)/repositories/RideRepository.cpp \
	$(SRC_DIR)/services/UserService.cpp \
	$(SRC_DIR)/services/DriverService.cpp \
	$(SRC_DIR)/services/BillingService.cpp \
	$(SRC_DIR)/services/RideService.cpp

OBJS := $(patsubst $(SRC_DIR)/%.cpp,$(BUILD_DIR)/%.o,$(SRCS))

all: $(TARGET)

$(TARGET): $(OBJS)
	@mkdir -p $(BUILD_DIR)
	$(CXX) $(CXXFLAGS) $(OBJS) -o $(TARGET)

$(BUILD_DIR)/%.o: $(SRC_DIR)/%.cpp
	@mkdir -p $(dir $@)
	$(CXX) $(CXXFLAGS) $(INC) -c $< -o $@

clean:
	rm -rf $(BUILD_DIR) $(TARGET)

.PHONY: all clean 