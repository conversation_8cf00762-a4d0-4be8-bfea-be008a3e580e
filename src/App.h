#pragma once

#include "Common.h"
#include "repositories/UserRepository.h"
#include "repositories/DriverRepository.h"
#include "repositories/RideRepository.h"
#include "services/UserService.h"
#include "services/DriverService.h"
#include "services/RideService.h"
#include "services/BillingService.h"

class App {
public:
	App();
	void handleCommand(const vector<string> &args);
	void printHelp() const;
private:
	UserRepository userRepository;
	DriverRepository driverRepository;
	RideRepository rideRepository;
	UserService userService;
	DriverService driverService;
	BillingService billingService;
	RideService rideService;

	void cmdAddUser(const vector<string> &args);
	void cmdUpdateUser(const vector<string> &args);
	void cmdUpdateUserLocation(const vector<string> &args);
	void cmdAddDriver(const vector<string> &args);
	void cmdUpdateDriverLocation(const vector<string> &args);
	void cmdChangeDriverStatus(const vector<string> &args);
	void cmdFindRide(const vector<string> &args);
	void cmdChooseRide(const vector<string> &args);
	void cmdCalculateBill(const vector<string> &args);
	void cmdFindTotalEarning(const vector<string> &args);
	void cmdCompleteRide(const vector<string> &args);
}; 