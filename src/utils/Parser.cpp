#include "Parser.h"
#include <sstream>

vector<string> Parser::split(const string &line) {
	vector<string> tokens;
	string current;
	int parenDepth = 0;
	for (char c : line) {
		if (c == '(') parenDepth++;
		if (c == ')') parenDepth--;
		if (c == ' ' && parenDepth == 0) {
			if (!current.empty()) {
				tokens.push_back(current);
				current.clear();
			}
		} else {
			current.push_back(c);
		}
	}
	if (!current.empty()) tokens.push_back(current);
	return tokens;
} 