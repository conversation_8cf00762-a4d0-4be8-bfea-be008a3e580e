#pragma once

#include "../Common.h"
#include "Location.h"
#include "Vehicle.h"

enum class DriverStatus { Available, Unavailable };

struct Driver {
	string name;
	string gender;
	int age;
	Vehicle vehicle;
	Location currentLocation;
	DriverStatus status{DriverStatus::Available};
	double totalEarnings{0.0};

	Driver() : age(0) {}
	Driver(string name_, string gender_, int age_, Vehicle vehicle_, Location location_)
		: name(std::move(name_)), gender(std::move(gender_)), age(age_), vehicle(std::move(vehicle_)), currentLocation(location_) {}
}; 