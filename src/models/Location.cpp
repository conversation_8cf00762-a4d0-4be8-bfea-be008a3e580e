#include "Location.h"
#include <sstream>
#include <cmath>

Location Location::fromString(const string &arg) {
	// Accept formats: "x,y" or "(x,y)"
	string s = arg;
	if (!s.empty() && s.front() == '(' && s.back() == ')') {
		s = s.substr(1, s.size() - 2);
	}
	for (char &c : s) {
		if (c == ' ') c = '\0';
	}
	stringstream ss(s);
	double x = 0.0, y = 0.0;
	char comma;
	ss >> x >> comma >> y;
	return Location{x, y};
}

double computeEuclideanDistance(const Location &a, const Location &b) {
	double dx = a.x - b.x;
	double dy = a.y - b.y;
	return sqrt(dx * dx + dy * dy);
} 