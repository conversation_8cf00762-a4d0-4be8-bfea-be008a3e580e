#pragma once

#include "../Common.h"
#include "../repositories/UserRepository.h"

class UserService {
public:
	explicit UserService(UserRepository &repo) : repository(repo) {}
	bool addUser(const string &name, const string &gender, int age);
	bool updateUser(const string &name, const string &gender, int age);
	bool updateUserLocation(const string &name, const Location &location);
	optional<User> getUser(const string &name) const { return repository.getByName(name); }
private:
	UserRepository &repository;
}; 