#include "DriverService.h"

bool DriverService::addDriver(const string &name, const string &gender, int age, const Vehicle &vehicle, const Location &location) {
	Driver driver{name, gender, age, vehicle, location};
	return repository.save(driver);
}

bool DriverService::updateDriverLocation(const string &name, const Location &location) {
	return repository.updateLocation(name, location);
}

bool DriverService::updateDriverStatus(const string &name, DriverStatus status) {
	return repository.updateStatus(name, status);
} 