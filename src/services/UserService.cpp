#include "UserService.h"

bool UserService::addUser(const string &name, const string &gender, int age) {
	User user{name, gender, age};
	return repository.save(user);
}

bool UserService::updateUser(const string &name, const string &gender, int age) {
	return repository.updateProfile(name, gender, age);
}

bool UserService::updateUserLocation(const string &name, const Location &location) {
	return repository.updateLocation(name, location);
} 