#include "RideService.h"

vector<RideOption> RideService::findRide(const string &userName, const Location &source, const Location &) const {
	vector<RideOption> options;
	auto maybeUser = userRepository.getByName(userName);
	if (!maybeUser) return options;

	for (const auto &driver : driverRepository.listAll()) {
		if (driver.status != DriverStatus::Available) continue;
		double distance = computeEuclideanDistance(source, driver.currentLocation);
		if (distance <= 5.0) {
			options.push_back(RideOption{driver.name, distance});
		}
	}
	// sort by nearest
	sort(options.begin(), options.end(), [](const RideOption &a, const RideOption &b){ return a.distanceFromUser < b.distanceFromUser; });
	return options;
}

bool RideService::chooseRide(const string &userName, const string &driverName, const Location &source, const Location &destination, double &outBill) {
	auto maybeUser = userRepository.getByName(userName);
	auto maybeDriver = driverRepository.getByName(driverName);
	if (!maybeUser || !maybeDriver) return false;
	if (maybeDriver->status != DriverStatus::Available) return false;
	if (computeEuclideanDistance(source, maybeDriver->currentLocation) > 5.0) return false;

	Ride ride{userName, driverName, source, destination};
	if (!rideRepository.saveActiveRide(ride)) return false;

	outBill = billingService.calculateBill(source, destination);
	driverRepository.addEarning(driverName, outBill);
	driverRepository.updateStatus(driverName, DriverStatus::Unavailable);
	return true;
}

bool RideService::completeRide(const string &userName) {
	auto active = rideRepository.getActiveRideForUser(userName);
	if (!active) return false;
	rideRepository.completeRideForUser(userName);
	// Mark driver available again
	driverRepository.updateStatus(active->driverName, DriverStatus::Available);
	// Move driver to destination as current location
	driverRepository.updateLocation(active->driverName, active->destination);
	return true;
}
