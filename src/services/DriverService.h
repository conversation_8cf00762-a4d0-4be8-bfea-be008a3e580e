#pragma once

#include "../Common.h"
#include "../repositories/DriverRepository.h"

class DriverService {
public:
	explicit DriverService(DriverRepository &repo) : repository(repo) {}
	bool addDriver(const string &name, const string &gender, int age, const Vehicle &vehicle, const Location &location);
	bool updateDriverLocation(const string &name, const Location &location);
	bool updateDriverStatus(const string &name, DriverStatus status);
	optional<Driver> getDriver(const string &name) const { return repository.getByName(name); }
	vector<Driver> listAll() const { return repository.listAll(); }
	bool addEarning(const string &name, double amount) { return repository.addEarning(name, amount); }
private:
	DriverRepository &repository;
}; 