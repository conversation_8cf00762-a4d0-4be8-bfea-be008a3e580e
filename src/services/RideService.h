#pragma once

#include "../Common.h"
#include "../repositories/RideRepository.h"
#include "../repositories/UserRepository.h"
#include "../repositories/DriverRepository.h"
#include "BillingService.h"

struct RideOption {
	string driverName;
	double distanceFromUser;
};

class RideService {
public:
	RideService(RideRepository &rideRepo, UserRepository &userRepo, DriverRepository &driverRepo, const BillingService &billing)
		: rideRepository(rideRepo), userRepository(userRepo), driverRepository(driverRepo), billingService(billing) {}

	vector<RideOption> findRide(const string &userName, const Location &source, const Location &destination) const;
	bool chooseRide(const string &userName, const string &driverName, const Location &source, const Location &destination, double &outBill);
	bool completeRide(const string &userName);

private:
	RideRepository &rideRepository;
	UserRepository &userRepository;
	DriverRepository &driverRepository;
	const BillingService &billingService;
}; 