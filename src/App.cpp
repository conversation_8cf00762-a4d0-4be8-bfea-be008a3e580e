#include "App.h"
#include <iostream>
#include <iomanip>

App::App()
	: userService(userRepository),
	driverService(driverRepository),
	billingService(),
	rideService(rideRepository, userRepository, driverRepository, billingService) {}

void App::printHelp() const {
	cout << "Commands:\n"
			  << " add_user <Name> <Gender> <Age>\n"
			  << " update_user <Name> <Gender> <Age>\n"
			  << " update_userLocation <Name> (<x,y>)\n"
			  << " add_driver <Name> <Gender> <Age> <VehicleModel> <RegNo> (<x,y>)\n"
			  << " update_driverLocation <Name> (<x,y>)\n"
			  << " change_driver_status <Name> <available|unavailable>\n"
			  << " find_ride <UserName> (<sx,sy>) (<dx,dy>)\n"
			  << " choose_ride <UserName> <DriverName> (<sx,sy>) (<dx,dy>)\n"
			  << " complete_ride <UserName>\n"
			  << " calculate_bill <UserName> (<sx,sy>) (<dx,dy>)\n"
			  << " find_total_earning\n"
			  << " help | quit\n";
}

void App::handleCommand(const vector<string> &args) {
	if (args.empty()) return;
	const string &cmd = args[0];
	if (cmd == "help") { printHelp(); return; }
	if (cmd == "add_user") { cmdAddUser(args); return; }
	if (cmd == "update_user") { cmdUpdateUser(args); return; }
	if (cmd == "update_userLocation") { cmdUpdateUserLocation(args); return; }
	if (cmd == "add_driver") { cmdAddDriver(args); return; }
	if (cmd == "update_driverLocation") { cmdUpdateDriverLocation(args); return; }
	if (cmd == "change_driver_status") { cmdChangeDriverStatus(args); return; }
	if (cmd == "find_ride") { cmdFindRide(args); return; }
	if (cmd == "choose_ride") { cmdChooseRide(args); return; }
	if (cmd == "complete_ride") { cmdCompleteRide(args); return; }
	if (cmd == "calculate_bill") { cmdCalculateBill(args); return; }
	if (cmd == "find_total_earning") { cmdFindTotalEarning(args); return; }
	cout << "Unknown command. Type 'help'.\n";
}

void App::cmdAddUser(const vector<string> &args) {
	if (args.size() != 4) { cout << "Usage: add_user <Name> <Gender> <Age>\n"; return; }
	bool ok = userService.addUser(args[1], args[2], stoi(args[3]));
	cout << (ok ? "User added\n" : "User already exists\n");
}

void App::cmdUpdateUser(const vector<string> &args) {
	if (args.size() != 4) { cout << "Usage: update_user <Name> <Gender> <Age>\n"; return; }
	bool ok = userService.updateUser(args[1], args[2], stoi(args[3]));
	cout << (ok ? "User updated\n" : "User not found\n");
}

void App::cmdUpdateUserLocation(const vector<string> &args) {
	if (args.size() != 3) { cout << "Usage: update_userLocation <Name> (<x,y>)\n"; return; }
	bool ok = userService.updateUserLocation(args[1], Location::fromString(args[2]));
	cout << (ok ? "User location updated\n" : "User not found\n");
}

void App::cmdAddDriver(const vector<string> &args) {
	if (args.size() != 7) { cout << "Usage: add_driver <Name> <Gender> <Age> <VehicleModel> <RegNo> (<x,y>)\n"; return; }
	Vehicle v{args[4], args[5]};
	bool ok = driverService.addDriver(args[1], args[2], stoi(args[3]), v, Location::fromString(args[6]));
	cout << (ok ? "Driver added\n" : "Driver already exists\n");
}

void App::cmdUpdateDriverLocation(const vector<string> &args) {
	if (args.size() != 3) { cout << "Usage: update_driverLocation <Name> (<x,y>)\n"; return; }
	bool ok = driverService.updateDriverLocation(args[1], Location::fromString(args[2]));
	cout << (ok ? "Driver location updated\n" : "Driver not found\n");
}

void App::cmdChangeDriverStatus(const vector<string> &args) {
	if (args.size() != 3) { cout << "Usage: change_driver_status <Name> <available|unavailable>\n"; return; }
	DriverStatus status = (args[2] == "available") ? DriverStatus::Available : DriverStatus::Unavailable;
	bool ok = driverService.updateDriverStatus(args[1], status);
	cout << (ok ? "Driver status updated\n" : "Driver not found\n");
}

void App::cmdFindRide(const vector<string> &args) {
	if (args.size() != 4) { cout << "Usage: find_ride <UserName> (<sx,sy>) (<dx,dy>)\n"; return; }
	auto options = rideService.findRide(args[1], Location::fromString(args[2]), Location::fromString(args[3]));
	if (options.empty()) { cout << "No ride found\n"; return; }
	cout << "Available drivers: ";
	for (size_t i = 0; i < options.size(); ++i) {
		cout << options[i].driverName;
		if (i + 1 != options.size()) cout << ", ";
	}
	cout << "\n";
}

void App::cmdChooseRide(const vector<string> &args) {
	if (args.size() != 5) { cout << "Usage: choose_ride <UserName> <DriverName> (<sx,sy>) (<dx,dy>)\n"; return; }
	double bill = 0.0;
	bool ok = rideService.chooseRide(args[1], args[2], Location::fromString(args[3]), Location::fromString(args[4]), bill);
	if (!ok) { cout << "Ride not found\n"; return; }
	cout << "Ride chosen. Bill: " << fixed << setprecision(0) << bill << "\n";
}

void App::cmdCompleteRide(const vector<string> &args) {
	if (args.size() != 2) { cout << "Usage: complete_ride <UserName>\n"; return; }
	bool ok = rideService.completeRide(args[1]);
	cout << (ok ? "Ride completed\n" : "No active ride for user\n");
}

void App::cmdCalculateBill(const vector<string> &args) {
	if (args.size() != 4) { cout << "Usage: calculate_bill <UserName> (<sx,sy>) (<dx,dy>)\n"; return; }
	double bill = billingService.calculateBill(Location::fromString(args[2]), Location::fromString(args[3]));
	cout << fixed << setprecision(0) << bill << "\n";
}

void App::cmdFindTotalEarning(const vector<string> &args) {
	(void)args;
	double total = 0.0;
	for (const auto &d : driverService.listAll()) total += d.totalEarnings;
	cout << fixed << setprecision(0) << total << "\n";
} 