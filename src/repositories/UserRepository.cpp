#include "UserRepository.h"

bool UserRepository::save(const User &user) {
	return nameToUser.emplace(user.name, user).second;
}

optional<User> UserRepository::getByName(const string &name) const {
	auto it = nameToUser.find(name);
	if (it == nameToUser.end()) return nullopt;
	return it->second;
}

bool UserRepository::updateLocation(const string &name, const Location &location) {
	auto it = nameToUser.find(name);
	if (it == nameToUser.end()) return false;
	it->second.currentLocation = location;
	return true;
}

bool UserRepository::updateProfile(const string &name, const string &gender, int age) {
	auto it = nameToUser.find(name);
	if (it == nameToUser.end()) return false;
	it->second.gender = gender;
	it->second.age = age;
	return true;
} 