#include "RideRepository.h"

bool RideRepository::saveActiveRide(const Ride &ride) {
	return userToActiveRide.emplace(ride.userName, ride).second;
}

optional<Ride> RideRepository::getActiveRideForUser(const string &userName) const {
	auto it = userToActiveRide.find(userName);
	if (it == userToActiveRide.end()) return nullopt;
	return it->second;
}

bool RideRepository::completeRideForUser(const string &userName) {
	auto it = userToActiveRide.find(userName);
	if (it == userToActiveRide.end()) return false;
	it->second.isCompleted = true;
	userToActiveRide.erase(it);
	return true;
} 