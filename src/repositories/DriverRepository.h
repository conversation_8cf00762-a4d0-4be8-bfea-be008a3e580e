#pragma once

#include "../Common.h"
#include "../models/Driver.h"

class DriverRepository {
public:
	bool save(const Driver &driver);
	optional<Driver> getByName(const string &name) const;
	bool updateLocation(const string &name, const Location &location);
	bool updateStatus(const string &name, DriverStatus status);
	bool addEarning(const string &name, double amount);
	vector<Driver> listAll() const;
private:
	unordered_map<string, Driver> nameToDriver;
}; 