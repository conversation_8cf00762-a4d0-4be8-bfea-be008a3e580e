#include "DriverRepository.h"

bool DriverRepository::save(const Driver &driver) {
	return nameToDriver.emplace(driver.name, driver).second;
}

optional<Driver> DriverRepository::getByName(const string &name) const {
	auto it = nameToDriver.find(name);
	if (it == nameToDriver.end()) return nullopt;
	return it->second;
}

bool DriverRepository::updateLocation(const string &name, const Location &location) {
	auto it = nameToDriver.find(name);
	if (it == nameToDriver.end()) return false;
	it->second.currentLocation = location;
	return true;
}

bool DriverRepository::updateStatus(const string &name, DriverStatus status) {
	auto it = nameToDriver.find(name);
	if (it == nameToDriver.end()) return false;
	it->second.status = status;
	return true;
}

bool DriverRepository::addEarning(const string &name, double amount) {
	auto it = nameToDriver.find(name);
	if (it == nameToDriver.end()) return false;
	it->second.totalEarnings += amount;
	return true;
}

vector<Driver> DriverRepository::listAll() const {
	vector<Driver> res;
	res.reserve(nameToDriver.size());
	for (const auto &p : nameToDriver) res.push_back(p.second);
	return res;
} 